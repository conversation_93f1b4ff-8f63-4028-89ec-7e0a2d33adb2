<template>
    <div class="article-list">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;任务列队</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="updateTaskList">刷新页面</el-button>
            </div>

            <div class="right">
                <el-select v-model="clientId" placeholder="客户端列表" clearable @change="updateTaskList">
                    <el-option v-for="item in clientList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </div>

        <!-- 内容栏 -->
        <el-table :data="taskQueue" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="状态" prop="state" width="200" align="center" />
            <el-table-column label="用户" align="center" width="220">
                <span slot-scope="scope">用户 {{scope.row.username}} <br /> 密码 {{scope.row.password}}</span>
            </el-table-column>
            <el-table-column label="类型" width="200" prop="type" align="center" />
            <el-table-column label="学校" prop="schoolname" align="center" />
            <el-table-column label="备注" prop="comment" align="center" sortable />
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button type="danger" icon="el-icon-delete" size="mini" @click="deleteTask(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码栏 -->
        <el-pagination class="pagination-container" :page-sizes="[5, 10, 20, 50]" :current-page="currentPage" :page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
    </div>
</template>

<script>
import * as clientApi from '../../api/clientApi.js';
export default {
    data() {
        return {
            //底部页码
            currentPage: 1, //当前页码
            pageSize: 10, //每页显示数量
            total: 0, //总的数量

            // 表格相关
            taskQueue: [], //表格数据
            multipleSelection: [], //表格多选

            //控制栏
            // 任务栏下拉列表
            clientList: [
                {
                    value: '等待',
                    label: '等待'
                },
            ],
            clientId: '',
        };
    },
    methods: {
        // ========= 控制栏 =========
        // 获取客户端列表
        async getClientList() {
            let clientList = await clientApi.getClientList();
            this.clientList = clientList.map(item => {
                return {
                    value: item.pcId,
                    label: item.pcId
                };
            });
            if (this.clientList.length > 0) {
                this.clientId = this.clientList[0].value;
            }
        },

        // ========= 表格内容栏 =========
        //获取表格数据
        async updateTaskList() {
            if (this.clientId) {
                //从服务器抓取数据
                let taskListResult = await clientApi.getClientQueue({
                    currentPage: this.currentPage,
                    pageSize: this.pageSize,
                    pcId: this.clientId,
                });
                let taskQueue = taskListResult.taskQueue;
                this.taskQueue = taskQueue;
                //更新本地页码数据
                this.total = taskListResult.pagination.total;
            }
        },

        // 删除任务
        async deleteTask(row) {
            try {
                await this.$confirm('确认删除该任务?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                await clientApi.delClient({
                    pcId: row.id
                });
                
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                
                // 刷新任务列表
                await this.updateTaskList();
            } catch (error) {
                // 用户取消删除或删除失败
                if (error !== 'cancel') {
                    this.$message({
                        type: 'error',
                        message: '删除失败: ' + (error.message || error)
                    });
                }
            }
        },

        // ========= 页码栏 =========
        //改变每页显示数量
        async sizeChange(size) {
            this.pageSize = size;
            await this.updateTaskList();
        },

        //改变页码
        async currentChange(current) {
            this.currentPage = current;
            await this.updateTaskList();
        },

        // ========= 工具方法 =========
        // 把秒转为 小时分钟秒：130->0时2分10秒
        secondsToMinSec(totalSeconds) {
            if (totalSeconds) {
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = Math.floor(totalSeconds % 60)
                return `${hours}时${minutes}分${seconds}秒`;
            } else {
                return '/';
            }
        },

    },
    async created() {
        await this.getClientList();
        await this.updateTaskList();
    },
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

.control {
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 10px;

    .left {
        float: left;
        .el-input {
            width: 150px;
        }
    }

    .right {
        float: right;
        display: flex;
        width: 500px;
        justify-content: space-between;
        & > * {
            flex: 1 1 auto;
            margin-right: 5px;
        }
        .el-input {
            width: 300px;
        }
    }
}

.content_button {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    & > * {
        margin-top: 5px;
    }
}

.logger {
    text-align: left;
    line-height: 15px;
    height: 100px;
    overflow: auto;
}
</style>