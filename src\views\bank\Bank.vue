<template>
    <div class="article-list" v-loading="loading" element-loading-text="加载中...">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;题库管理</div>

        <!-- 搜索与控制栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchParams">
                <div class="search-inputs">
                    <el-form-item label="题目">
                        <el-input v-model="searchParams.content" placeholder="题目内容" size="medium"></el-input>
                    </el-form-item>
                    <el-form-item label="平台">
                        <el-select v-model="searchParams.platform" placeholder="请选择平台" size="medium" clearable>
                            <el-option label="安徽继续教育在线" value="安徽继续教育在线"></el-option>
                            <el-option label="超星学习通" value="超星学习通"></el-option>
                            <el-option label="国家开放大学" value="国家开放大学"></el-option>
                            <el-option label="弘成教育" value="弘成教育"></el-option>
                            <el-option label="柠檬文采学堂" value="柠檬文采学堂"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="类型">
                        <el-input v-model="searchParams.type" placeholder="类型" size="medium"></el-input>
                    </el-form-item>
                    <el-form-item label="课程">
                        <el-input v-model="searchParams.course_name" placeholder="课程名" size="medium"></el-input>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input v-model="searchParams.comment" placeholder="备注" size="medium"></el-input>
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-input v-model="searchParams.keyword" placeholder="请输入日期" size="medium"></el-input>
                    </el-form-item>
                </div>
                <div class="search-actions">
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="handleSearch">查询</el-button>
                    <el-button icon="el-icon-refresh" size="medium" @click="resetSearch">重置</el-button>
                    <el-button type="danger" icon="el-icon-delete" size="medium" @click="delQuestion" v-if="isAdmin">删除选中</el-button>
                </div>
            </el-form>
        </div>

        <!-- 内容栏 -->
        <el-table :data="bankList" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="内容" prop="content"  align="center">
                <template slot-scope="scope">
                    <span @click="handleContentClick(scope.row)">{{ scope.row.content }}</span>
                </template>
            </el-table-column>
            <el-table-column label="选项" prop="options" width="500" align="center" />
            <el-table-column label="答案" prop="answers"  align="center" />
            <el-table-column label="平台" prop="platform" width="150"  align="center" />
            <el-table-column label="类型" prop="type" width="100"  align="center" />
            <el-table-column label="课程名" prop="course_name" width="100"  align="center" />
            <el-table-column label="添加时间" prop="add_time" width="150"  align="center" />
        </el-table>

        <!-- 分页 -->
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            style="margin-top: 20px; text-align: right;">
        </el-pagination>

        <!-- 题目详情弹窗 -->
        <el-dialog
            title="题目详情"
            :visible.sync="detailDialogVisible"
            width="60%"
            top="5vh">
            <el-form :model="editForm" label-width="100px">
                <!-- 可编辑字段 -->
                <el-form-item label="题目内容">
                    <el-input 
                        v-model="editForm.content" 
                        type="textarea"
                        :rows="3"
                        placeholder="请输入题目内容">
                    </el-input>
                </el-form-item>
                <el-form-item label="答案">
                    <el-input 
                        v-model="editForm.answers" 
                        type="textarea"
                        :rows="2"
                        placeholder="请输入答案">
                    </el-input>
                </el-form-item>
                <el-form-item label="答案内容">
                    <el-input 
                        v-model="editForm.answers_content" 
                        type="textarea"
                        :rows="2"
                        placeholder="请输入答案内容">
                    </el-input>
                </el-form-item>
                <el-form-item label="选项">
                    <el-input 
                        v-model="editForm.options" 
                        type="textarea"
                        :rows="3"
                        placeholder="请输入选项内容">
                    </el-input>
                </el-form-item>
                
                <!-- 只读字段 -->
                <el-form-item label="平台">
                    <el-input :value="currentQuestion.platform" disabled></el-input>
                </el-form-item>
                <el-form-item label="类型">
                    <el-input :value="currentQuestion.type" disabled></el-input>
                </el-form-item>
                <el-form-item label="课程名">
                    <el-input 
                        v-model="editForm.course_name" 
                        placeholder="请输入课程名">
                    </el-input>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input 
                        v-model="editForm.comment" 
                        type="textarea"
                        :rows="2"
                        placeholder="请输入备注">
                    </el-input>
                </el-form-item>
                <el-form-item label="添加时间">
                    <el-input :value="currentQuestion.add_time" disabled></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="detailDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="confirmEdit" v-if="isAdmin">确认修改</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import * as bankApi from '../../api/bankApi.js';
export default {
    data() {
        return {
            loading: false, // 加载状态
            // 权限
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',
            
            // 表格相关
            bankList: [], //表格数据
            multipleSelection: [], //表格多选

            //控制栏
            searchParams: {
                content: '',
                platform: '',
                type: '',
                course_name: '',
                comment: '',
                keyword: ''
            },

            // 分页
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },

            // 题目详情弹窗
            detailDialogVisible: false, // 题目详情弹窗开关
            currentQuestion: {}, // 当前选中的题目
            editForm: {
                content: '',
                answers: '',
                answers_content: '',
                options: '',
                course_name: '',
                comment: ''
            }, // 编辑表单数据
        };
    },
    methods: {
        // ========= 控制栏 =========
        // 删除选中
        async delQuestion() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要删除的题目');
                return;
            }
            this.loading = true;
            try {
                const ids = this.multipleSelection.map(item => item.id);
                await bankApi.deleteQuestion({ ids });
                this.$message.success('删除成功');
                await this.getBankList();
            } catch (error) {
                console.error('Delete question failed:', error);
                this.$message.error('删除失败，请稍后重试');
                this.loading = false;
            }
        },

        handleSearch() {
            this.pagination.currentPage = 1;
            this.getBankList();
        },

        resetSearch() {
            this.searchParams = {
                content: '',
                platform: '',
                type: '',
                course_name: '',
                comment: '',
                keyword: ''
            };
            this.getBankList();
        },

        // ========= 表格内容栏 =========
        // 获取题库列表
        async getBankList() {
            this.loading = true;
            try {
                let params = {
                    ...this.searchParams,
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize
                };
                let res = await bankApi.getBankList(params);
                if (res) {
                    const processedList = res.list.map(item => {
                        const formatText = (text) => {
                            if (text && text.length > 100) {
                                return text.substring(0, 100) + '...';
                            }
                            return text;
                        };
                        const formatDate = (date) => {
                            if (date) {
                                return new Date(date).toLocaleString();
                            }
                            return '';
                        }
                        return {
                            ...item,
                            // 保存原始数据
                            original_content: item.content,
                            original_options: item.options,
                            original_answers: item.answers,
                            original_answers_content: item.answers_content,
                            // 格式化显示数据
                            content: formatText(item.content),
                            options: formatText(item.options),
                            answers: formatText(item.answers),
                            add_time: formatDate(item.add_time)
                        };
                    });
                    this.bankList = processedList;
                    this.pagination.total = res.pagination.total;
                }
            } catch (error) {
                console.error("Failed to get bank list:", error);
                this.$message.error('获取题库列表失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // ========= 分页 =========
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getBankList();
        },
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.getBankList();
        },

        handleContentClick(row) {
            // 保存当前行数据，注意这里需要获取完整的数据
            // row中的content等字段可能被截断，需要找到原始完整数据
            this.currentQuestion = { ...row };
            
            // 如果row.id存在，可以考虑从服务器获取完整数据
            // 这里暂时使用row中的数据，如果需要完整数据可以调用API
            
            // 初始化编辑表单，使用原始数据（如果有的话）
            this.editForm = {
                content: row.original_content || row.content || '',
                answers: row.original_answers || row.answers || '',
                answers_content: row.original_answers_content || row.answers_content || '',
                options: row.original_options || row.options || '',
                course_name: row.course_name || '',
                comment: row.comment || ''
            };
            
            // 打开弹窗
            this.detailDialogVisible = true;
        },

        // 确认修改题目
        async confirmEdit() {
            this.loading = true;
            try {
                let data = {
                    id: this.currentQuestion.id,
                    content: this.editForm.content,
                    answers: this.editForm.answers,
                    answers_content: this.editForm.answers_content,
                    options: this.editForm.options,
                    course_name: this.editForm.course_name,
                    comment: this.editForm.comment
                };
                await bankApi.alertQuestion(data);
                this.$message.success('修改成功');
                this.detailDialogVisible = false;
                // 刷新列表
                await this.getBankList();
            } catch (error) {
                console.error('Update question failed:', error);
                this.$message.error('修改失败，请稍后重试');
                this.loading = false;
            }
        },

        
    },
    async created() {
        await this.getBankList();
    },
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

.search-container {
    border: 1px solid #ebeef5;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.search-inputs {
    display: flex;
    flex-wrap: wrap;
}

.search-container .el-form-item {
    flex: 1 1 30%;
    margin-right: 10px;
    margin-bottom: 10px;
}

.search-actions {
    margin-top: 10px;
}

.control {
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 10px;

    .left {
        float: left;
        display: flex;
        & > * {
            margin-right: 10px;
        }
        .el-input {
            width: 150px;
        }
    }

    .right {
        float: right;
        display: flex;
        width: 500px;
        justify-content: space-between;
        & > * {
            flex: 1 1 auto;
            margin-right: 5px;
        }
        .el-input {
            width: 300px;
        }
    }
}

.content_button {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    & > * {
        margin-top: 5px;
    }
}

.logger {
    text-align: left;
    line-height: 15px;
    height: 100px;
    overflow: auto;
}
</style>