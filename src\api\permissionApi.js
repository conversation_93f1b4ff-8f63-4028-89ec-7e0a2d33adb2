import axios from "@/utils/axios.js";

// 权限管理API

// 1.获取权限树形结构
export let getPermissionTree = () => axios.get('/api/admin/permission/getTree');

// 2.获取权限列表（平铺结构）
export let getPermissionList = (params) => axios.get('/api/admin/permission/getList', { params });

// 3.创建权限
export let createPermission = (data) => axios.post('/api/admin/permission/create', data);

// 4.修改权限
export let updatePermission = (data) => axios.post('/api/admin/permission/update', data);

// 5.删除权限
export let deletePermission = (data) => axios.post('/api/admin/permission/delete', data); 