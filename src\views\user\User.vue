<template>
    <div class="article-list" v-loading="loading" element-loading-text="加载中...">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;用户管理</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-plus" size="medium" @click="handleAdd" v-if="isAdmin">新增用户</el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="handleBatchDelete" v-if="isAdmin">批量删除</el-button>
            </div>
            <div class="right">
                <el-input placeholder="请输入用户名或邮箱搜索" v-model="searchKeyword" clearable @keyup.enter="handleSearch">
                    <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                </el-input>
            </div>
        </div>

        <!-- 表格内容栏 -->
        <el-table 
            v-if="userList.length > 0"
            :key="tableKey"
            :data="userList" 
            :row-key="user => user.user_id" 
            border 
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" prop="user_id" width="80" align="center" />
            <el-table-column label="用户名" align="center">
                <template slot-scope="scope">
                    <el-link 
                        type="primary" 
                        @click="handleEdit(scope.row)"
                        :underline="false">
                        {{ scope.row.user_name }}
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column label="邮箱" prop="user_email" align="center" />
            <el-table-column label="角色" align="center">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.user_role_id == 1 ? 'danger' : 'primary'">
                        {{ scope.row.user_role ? scope.row.user_role.role_name : '未知' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="注册时间" width="180" align="center">
                <template slot-scope="scope">
                    {{ formatDate(scope.row.user_signup_time) }}
                </template>
            </el-table-column>
            <el-table-column label="最后登录时间" width="180" align="center">
                <template slot-scope="scope">
                    {{ formatDate(scope.row.user_signin_time) }}
                </template>
            </el-table-column>
            <el-table-column label="最后登录IP" prop="user_signin_ip" width="150" align="center" />
        </el-table>

        <!-- 分页 -->
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            style="margin-top: 20px; text-align: right;">
        </el-pagination>

        <!-- 新增/编辑用户弹窗 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="500px"
            @close="resetForm">
            <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
                <el-form-item label="用户名" prop="user_name">
                    <el-input v-model="userForm.user_name" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="邮箱" prop="user_email" v-if="!isEdit">
                    <el-input v-model="userForm.user_email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="user_password">
                    <el-input 
                        v-model="userForm.user_password" 
                        :type="showPassword ? 'text' : 'password'" 
                        :placeholder="isEdit ? '当前密码（可修改）' : '请输入密码'">
                        <i 
                            slot="suffix" 
                            :class="showPassword ? 'el-icon-view' : 'el-icon-view'"
                            @click="showPassword = !showPassword"
                            style="cursor: pointer; color: #909399; margin-right: 10px;"
                            :title="showPassword ? '隐藏密码' : '显示密码'">
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item label="角色" prop="user_role_id">
                    <el-select v-model="userForm.user_role_id" placeholder="请选择角色">
                        <el-option 
                            v-for="role in roleList" 
                            :key="role.role_id" 
                            :label="role.role_name || role.role_id" 
                            :value="role.role_id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button type="danger" @click="handleDeleteInDialog" v-if="isEdit && isAdmin">删 除</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import * as userApi from '../../api/userApi.js';

export default {
    data() {
        // 自定义验证规则
        const validateEmail = (rule, value, callback) => {
            const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (!emailReg.test(value)) {
                callback(new Error('请输入有效的邮箱地址'));
            } else {
                callback();
            }
        };

        return {
            loading: false, // 加载状态
            
            // 权限控制
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',
            
            // 表格相关
            userList: [], // 用户列表数据
            tableKey: 0, // 用于强制重新渲染表格
            multipleSelection: [], // 多选数据
            
            // 搜索相关
            searchKeyword: '', // 搜索关键词
            
            // 分页相关
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            
            // 弹窗相关
            dialogVisible: false, // 弹窗显示控制
            dialogTitle: '新增用户', // 弹窗标题
            isEdit: false, // 是否编辑模式
            showPassword: false, // 是否显示密码
            
            // 表单数据
            userForm: {
                user_id: null,
                user_name: '',
                user_email: '',
                user_password: '',
                user_role_id: null
            },
            
            // 表单验证规则
            userRules: {
                user_name: [
                    { required: true, message: '请输入用户名', trigger: 'blur' },
                    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                user_email: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { validator: validateEmail, trigger: 'blur' }
                ],
                user_password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                ],
                user_role_id: [
                    { required: true, message: '请选择角色', trigger: 'change', type: 'number' }
                ]
            },
            
            // 角色列表
            roleList: []
        };
    },
    
    methods: {
        // ========= 工具函数 =========
        // 格式化日期
        formatDate(date) {
            if (!date) return '';
            return new Date(date).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },
        
        // ========= 数据获取 =========
        // 获取用户列表
        async getUserList() {
            this.loading = true;
            try {
                const params = {
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize,
                    search: this.searchKeyword
                };
                
                const res = await userApi.getUserList(params);
                console.log('用户列表响应:', res);
                
                // axios拦截器已经返回了data部分，所以res就是data对象
                if (res) {
                    // 使用深拷贝处理数据，避免引用问题
                    const listData = JSON.parse(JSON.stringify(res.list || []));
                    // 增加表格key，强制重新渲染
                    this.tableKey += 1;
                    // 使用this.$nextTick确保在DOM更新循环结束后再赋值
                    this.$nextTick(() => {
                        this.userList = listData;
                    });
                    
                    this.pagination.total = res.total || 0;
                    this.pagination.currentPage = res.page || 1;
                    this.pagination.pageSize = res.pageSize || 10;
                }
            } catch (error) {
                console.error('获取用户列表失败:', error);
                this.$message.error('获取用户列表失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },
        
        // 获取角色列表
        async getRoleList() {
            try {
                const res = await userApi.getRoleList();
                console.log('角色列表响应:', res);
                
                // axios拦截器已经返回了data部分，所以res就是data对象
                if (res && res.list) {
                    // 将角色数据转换为统一格式
                    this.roleList = res.list.map(item => ({
                        role_id: item.role_id,
                        role_name: item.role_name
                    }));
                }
            } catch (error) {
                console.error('获取角色列表失败:', error);
                this.$message.error('获取角色列表失败');
            }
        },
        
        // ========= 表格操作 =========
        // 处理多选
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        
        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1;
            this.getUserList();
        },
        
        // ========= 分页操作 =========
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getUserList();
        },
        
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.getUserList();
        },
        
        // ========= CRUD操作 =========
        // 新增用户
        handleAdd() {
            this.dialogTitle = '新增用户';
            this.isEdit = false;
            this.showPassword = false; // 重置密码显示状态
            this.dialogVisible = true;
            this.resetForm();
        },
        
        // 编辑用户 - 根据API文档，编辑时不能修改邮箱
        handleEdit(row) {
            this.dialogTitle = '编辑用户';
            this.isEdit = true;
            this.userForm = {
                user_id: row.user_id,
                user_name: row.user_name,
                user_email: row.user_email, // 保留邮箱数据但不显示编辑
                user_password: row.user_password || '', // 显示当前密码
                user_role_id: row.user_role_id
            };
            this.showPassword = false; // 重置密码显示状态
            this.dialogVisible = true;
        },
        
        // 删除单个用户 - 根据最新API文档更新
        async handleDelete(row) {
            // 不能删除自己
            if (row.user_id === this.$store.state.user.userInfo.user_id) {
                this.$message.error('不能删除自己的账号');
                return;
            }
            
            this.$confirm(`确定要删除用户 "${row.user_name}" 吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 根据API文档，需要同时传递user_ids数组和user_id
                    await userApi.deleteUser({ 
                        user_ids: [row.user_id],
                        user_id: row.user_id 
                    });
                    this.$message.success('删除成功');
                    await this.getUserList();
                } catch (error) {
                    console.error('删除用户失败:', error);
                    this.$message.error('删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },
        
        // 批量删除 - 根据最新API文档更新
        async handleBatchDelete() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要删除的用户');
                return;
            }
            
            // 检查是否包含自己
            const currentUserId = this.$store.state.user.userInfo.user_id;
            const hasCurrentUser = this.multipleSelection.some(user => user.user_id === currentUserId);
            if (hasCurrentUser) {
                this.$message.error('不能删除自己的账号');
                return;
            }
            
            this.$confirm(`确定要删除选中的 ${this.multipleSelection.length} 个用户吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 收集所有要删除的用户ID
                    const userIds = this.multipleSelection.map(user => user.user_id);
                    
                    // 根据API文档，批量删除时传递所有用户ID
                    // 注意：这里user_id可以传递数组中的第一个ID或任意一个ID
                    await userApi.deleteUser({ 
                        user_ids: userIds,
                        user_id: userIds[0] // 使用第一个ID作为user_id
                    });
                    
                    this.$message.success(`成功删除 ${this.multipleSelection.length} 个用户`);
                    await this.getUserList();
                } catch (error) {
                    console.error('批量删除失败:', error);
                    this.$message.error('删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },
        
        // 提交表单
        submitForm() {
            this.$refs.userForm.validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    try {
                        if (this.isEdit) {
                            // 编辑模式 - 根据API文档，不再支持修改邮箱
                            const updateData = {
                                user_id: this.userForm.user_id,
                                user_name: this.userForm.user_name,
                                user_password: this.userForm.user_password, // 始终发送密码
                                user_role_id: Number(this.userForm.user_role_id)
                            };
                            
                            await userApi.updateUser(updateData);
                            this.$message.success('修改成功');
                        } else {
                            // 新增模式 - 需要所有字段
                            const createData = {
                                ...this.userForm,
                                user_role_id: Number(this.userForm.user_role_id)
                            };
                            await userApi.createUser(createData);
                            this.$message.success('新增成功');
                        }
                        
                        this.dialogVisible = false;
                        await this.getUserList();
                    } catch (error) {
                        console.error('操作失败:', error);
                        this.$message.error(error.response?.data?.message || '操作失败，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                } else {
                    console.log('表单验证失败');
                    return false;
                }
            });
        },
        
        // 重置表单
        resetForm() {
            this.userForm = {
                user_id: null,
                user_name: '',
                user_email: '',
                user_password: '',
                user_role_id: null
            };
            
            // 重置密码显示状态
            this.showPassword = false;
            
            // 根据模式设置密码验证规则
            if (this.isEdit) {
                // 编辑模式，密码必填（因为需要显示当前密码）
                this.userRules.user_password = [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                ];
            } else {
                // 新增模式，密码必填
                this.userRules.user_password = [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                ];
            }
            
            this.$nextTick(() => {
                this.$refs.userForm && this.$refs.userForm.clearValidate();
            });
        },
        
        // 在编辑弹窗中删除用户
        async handleDeleteInDialog() {
            const row = {
                user_id: this.userForm.user_id,
                user_name: this.userForm.user_name
            };
            
            // 不能删除自己
            if (row.user_id === this.$store.state.user.userInfo.user_id) {
                this.$message.error('不能删除自己的账号');
                return;
            }
            
            this.$confirm(`确定要删除用户 "${row.user_name}" 吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 根据API文档，需要同时传递user_ids数组和user_id
                    await userApi.deleteUser({ 
                        user_ids: [row.user_id],
                        user_id: row.user_id 
                    });
                    this.$message.success('删除成功');
                    this.dialogVisible = false; // 关闭弹窗
                    await this.getUserList();
                } catch (error) {
                    console.error('删除用户失败:', error);
                    this.$message.error('删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        }
    },
    
    async created() {
        // 页面加载时获取数据
        await this.getRoleList();
        await this.getUserList();
    }
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    min-height: calc(100vh - 100px);
}

.user-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409EFF;
}

.control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
        display: flex;
        gap: 10px;
    }
    
    .right {
        .el-input {
            width: 300px;
        }
    }
}

.dialog-footer {
    text-align: right;
}

// 加载动画样式
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
}
</style>