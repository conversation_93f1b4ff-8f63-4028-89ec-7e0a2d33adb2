<template>
    <div class="article-list">
        <!-- 自定义Loading组件 -->
        <Loading :visible="loading" :text="loadingText" :duration="loadingDuration" />

        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;{{meta.name}}</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="addOrEdit = true">添加任务</el-button>
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="multipleRunTask">开始选中</el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="multipleDelete('')" v-if="isAdmin">删除选中</el-button>
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="resetTask">重置任务</el-button>
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="()=>{autoRefresh=!autoRefresh;updateTaskList()}">{{refreshContent}}</el-button>
                <el-select v-if="meta.hasVideoRateColumn" v-model="videoRate" placeholder="视频速度">
                    <el-option v-for="item in rateOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button type="primary" size="medium">
                    <download-excel :fields="json_fields" name="courseResult.xls" :stringifyLongNum="true" :fetch="exportData">导出数据</download-excel>
                </el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="multipleDelete('失败')">删除失败</el-button>
            </div>
            <div class="right">
                <el-input placeholder="请输入用户名" v-model="usernameSearch" clearable>
                    <el-button slot="append" icon="el-icon-search" @click="startSearch"></el-button>
                </el-input>
                <el-input placeholder="请输入备注" v-model="commentSearch" clearable>
                    <el-button slot="append" icon="el-icon-search" @click="startSearch"></el-button>
                </el-input>
                <el-select v-model="stateSearch" placeholder="任务状态" clearable @change="startSearch">
                    <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button icon="el-icon-close" @click="clearSearch">清空</el-button>
            </div>
        </div>

        <!-- 内容栏 -->
        <el-table :data="taskList" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户信息" width="220" align="center">
                <span slot-scope="scope">用户 {{scope.row.username}} <br /> 密码 {{scope.row.password}}</span>
            </el-table-column>
            <el-table-column v-if="meta.hasPhotoColumn" label="照片" width="120" align="center">
                <template slot-scope='scope'>
                    <el-upload class="avatar-uploader" :show-file-list="false" :before-upload="(file)=>{beforeAvatarUpload(file,scope.row);return false}" action="">
                        <img v-if="scope.row.photoBase64" :src="scope.row.photoBase64" class="avatar image">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="state" width="80" align="center" sortable />
            <el-table-column label="任务日志" align="center">
                <template slot-scope="scope">
                    <div class="logger" @click='openLogger(scope.row)'>
                        <div v-html="scope.row.logMessage"></div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="课程进度" width="400" align="center">
                <template slot-scope="scope">
                    <div v-html="scope.row.progressMsg"></div>
                </template>
            </el-table-column>
            <el-table-column label="错误信息" width="300" align="center">
                <template slot-scope="scope">
                    <div class="logger" @click='openErrorLogger(scope.row.error_info)'>
                        <div v-html="scope.row.error_info"></div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="备注" width="80" prop="comment" align="center" sortable />
        </el-table>

        <!-- 页码栏 -->
        <el-pagination class="pagination-container" :page-sizes="[10, 20, 50,100]" :current-page="currentPage" :page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="sizeChange" @current-change="currentChange"></el-pagination>

        <!-- 弹窗，批量添加 用户名，密码，学校名 -->
        <el-dialog :visible.sync="addOrEdit" width="60%" top="3vh" title="批量添加">
            <el-input placeholder="请输入备注" v-model="comment"></el-input>
            <el-input placeholder="[username, password, schoolname, year, term, coursename] " type="textarea" :rows="5" v-model="courseData"></el-input>
            <div slot="footer">
                <el-button @click="addOrEdit = false">取 消</el-button>
                <el-button type="primary" @click="normalizedData">格式化</el-button>
                <el-button type="primary" @click="multipleAddTask" :disabled="buttonDisabled">添 加</el-button>
            </div>
            <div v-html="courseHtml"></div>
        </el-dialog>

        <!-- 弹窗，操作日志 -->
        <el-dialog :visible.sync="showLoginfoDialogVisible" width="30%" top="5vh" title="详细日志">
            <div v-html="logInfo"></div>
        </el-dialog>

        <!-- 弹窗，错误日志 -->
        <el-dialog :visible.sync="showErrorDialogVisible" width="30%" top="5vh" title="详细日志">
            <div v-html="errorInfo"></div>
        </el-dialog>

    </div>
</template>

<script>
import * as courseApi from '@/api/courseApi.js';
import * as clusterApi from '@/api/clusterApi.js';
import { v4 } from 'uuid';
import JsonExcel from 'vue-json-excel'
import Loading from '../component/Loading.vue'
export default {
    data() {
        return {
            //网站数据
            meta: {
                platform: '',
                name: '',
                hasPhotoColumn: false, //是否有人脸识别的列
                hasVideoRateColumn: false, // 是否有视频倍速的列
                passProgress: 95,
            },

            // 权限
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',

            //底部页码
            currentPage: 1, //当前页码
            pageSize: 10, //每页显示数量
            total: 0, //总的数量

            // 表格相关
            taskList: [], //表格数据
            multipleSelection: [], //表格多选
            videoRate: 1, //视频倍速
            autoRefresh: false, //自动刷新页面
            refreshContent: '开启刷新',

            //批量添加弹窗
            addOrEdit: false, //批量添加数据弹窗开关
            courseData: '', //批量添加数据
            courseHtml: '',
            buttonDisabled: true, //批量添加数据按钮禁用开关
            comment: '', //备注

            //任务日志弹窗
            showLoginfoDialogVisible: false, //修改用户信息弹窗开关
            logInfo: '',//日志数据

            //错误信息弹窗
            showErrorDialogVisible: false, //修改用户信息弹窗开关
            errorInfo: '',//错误日志数据

            //控制栏
            commentSearch: '',//搜索时候的备注
            usernameSearch: '', //用户输入的搜索内容
            stateOptions: [
                {
                    value: '等待',
                    label: '等待'
                },
                {
                    value: '正在进行',
                    label: '正在进行'
                },
                {
                    value: '完成',
                    label: '完成'
                },
                {
                    value: '列队中',
                    label: '列队中'
                },
                {
                    value: '失败',
                    label: '失败'
                },
                {
                    value: '服务器中断',
                    label: '服务器中断'
                },
                {
                    value: '客户端中断',
                    label: '客户端中断'
                },
            ],
            stateSearch: '',
            rateOptions: [
                {
                    value: 1,
                    label: '1倍'
                },
                {
                    value: 3,
                    label: '3倍'
                },
                {
                    value: 100,
                    label: '秒刷'
                },

            ],
            videoRate: 1, //视频倍速

            // 导出数据为excel表格
            // 表格数据
            excelpage: [],
            // 表格表头
            json_fields: {
                用户名: { field: "username" },
                密码: { field: "password", callback: (value) => `="${value}"` },
                学校: { field: "schoolname" },
                平台: { field: "platform" },
                状态: { field: "state" },
                科目: { field: "subject" },
                分数: { field: "score" },
                备注: { field: "comment" },
                最后一条日志: { field: "logMessage" },
            },
            json_meta: [
                [
                    {
                        " key ": " charset ",
                        " value ": " utf- 8 "
                    }
                ]
            ],

            // 等待界面
            loading: false,
            loadingText: '正在进行任务中...',
            loadingDuration: 0
        };
    },
    methods: {
        // ========= 工具函数 =========
        // 防抖
        debounce(fn, delay) {
            let timer = null;
            return function (...args) {
                if (timer) clearTimeout(timer); // 清除之前的定时器
                timer = setTimeout(() => {
                    fn.apply(this, args); // 在 delay 毫秒后执行
                }, delay);
            }
        },

        // ========= 控制栏 =========
        // 批量删除任务
        async multipleDelete(state) {
            let length = this.multipleSelection.length;
            if (length === 0) {
                this.$message.error('请先选择数据')
                return
            }

            for (let item of this.multipleSelection) {
                if (item.state == '正在进行') {
                    this.$message.error(`有任务正在进行，需要先重置或者等待其完成才能删除，${item.username}`);
                    return
                }
            }

            // 删除过滤条件
            if (state) {
                this.multipleSelection = this.multipleSelection.filter(item => item.state == state)
            }

            let courseTaskArr = this.multipleSelection.map(item => { return { id: item.id } })

            try {
                this.loading = true;
                this.loadingText = '正在删除任务...';
                this.loadingDuration = length * 500+2000; // 预计每个任务0.5秒
                let result = await courseApi.delCourseTask(courseTaskArr);
                await this.updateTaskList();
                this.loading = false;
                this.$message.success(`成功删除${result.totalCount}条数据 `);
            } catch (error) {
                this.loading = false;
                this.$message.error(error);
            }

        },
        //批量增加任务
        async multipleAddTask() {
            this.buttonDisabled = true; // 启用"添加"按钮
            let courseArr = JSON.parse(this.courseData);
            if (courseArr.length === 0) {
                this.$message.error('请先选择数据')
                return
            }
            courseArr = courseArr.map(item => {
                return {
                    id: v4(),
                    username: item.username,
                    password: item.password,
                    schoolname: item.schoolname,
                    platform: this.meta.platform,
                    year: item.year,
                    term: item.term,
                    coursename: item.coursename,
                    others: item.others,
                    // schoolurl: '', // 由服务器根据schoolname自动填充
                    state: '等待',
                    add_time: new Date(),
                    // start_time: '', // 由服务器自动填充
                    // end_time: '', // 由服务器自动填充
                    // error_info: '', // 由服务器自动填充
                    // final_result: '', // 由服务器自动填充
                    comment: this.comment,
                    user: this.$store.state.user.userInfo.user_email, //任务发起人
                }
            })

            try {
                this.loading = true;
                this.loadingText = '正在添加任务...';
                this.loadingDuration = courseArr.length * 500+2000; // 预计每个任务0. 5秒
                let result = await courseApi.addCourseTask(courseArr);
                // 重置添加任务弹窗的状态
                this.addOrEdit = false; // 关闭弹窗
                this.courseData = ''; // 清空数据
                this.comment = ''; // 清空备注
                this.courseHtml = ''; // 清空html
                await this.updateTaskList()
                this.loading = false;
                this.$message.success(`成功添加${result.totalCount}条数据，其中新增${result.createCount}条数据，更新${result.updateCount}条数据`);
            } catch (error) {
                this.loading = false;
                this.$message.error(error);
            }
        },
        //批量开始任务
        async multipleRunTask() {
            let length = this.multipleSelection.length;
            if (length === 0) {
                this.$message.error('请先选择数据')
                return
            }

            this.loading = true
            this.loadingText = '正在启动任务...';
            this.loadingDuration = this.multipleSelection.length * 600+2000; // 每个任务预计0.6秒
            let courseTaskArr = this.multipleSelection.map(item => { return { ...item, runCourse: true, runAssignment: true, videoRate: this.videoRate } })

            try {
                let runRes = await courseApi.runCourseTask(courseTaskArr);
                await this.updateTaskList()
                await new Promise(r => setTimeout(r, 500))
                await this.updateTaskList()
                this.loading = false

                this.$message.success(`成功开始${length}条数据，请手动开启进度更新，其中${runRes.runCount}条数据开始运行，${runRes.addCount}条数据加入列队 `);
            } catch (error) {
                this.loading = false
                this.$message.error(`任务开始失败：${error} `);
            }

        },
        //清空搜索内容
        async clearSearch() {
            this.usernameSearch = '';
            this.commentSearch = '';
            this.stateSearch = '';
            await this.updateTaskList()
        },
        // 开始搜索
        async startSearch() {
            this.currentPage = 1
            await this.updateTaskList()
        },
        //重置任务
        async resetTask() {
            let length = this.multipleSelection.length;
            if (length === 0) {
                this.$message.error('请先选择数据')
                return
            }
            // 开启loading
            this.loading = true
            this.loadingText = '正在重置任务...';
            this.loadingDuration = this.multipleSelection.length * 500+2000; // 每个任务预计0.5秒

            // //  “列队中”的任务暂时不处理
            // this.multipleSelection = this.multipleSelection.filter(item => item.state != '列队中')

            let runningTaskIdListWithReset = []
            // 对正在进程的任务进行重置操作 只用map会导致[nul]的情况
            let runningTaskIdList = this.multipleSelection
                .filter(item => item.state == '正在进行')
                .map(item => ({
                    id: item.id,
                }));

            if (runningTaskIdList.length > 0) {
                let runningClusterInfoList = await clusterApi.getClusterInfoByTaskId(runningTaskIdList)
                // 有些“正在进行”的任务，对应的进程有可能已经重置，所以找不到对应的clusterInfo
                // 这时候需要把runningTaskIdList分为两部分，一部分是有进程信息的，另一部分是找不到进程信息的。
                runningTaskIdList.forEach(runningTaskId => {
                    let clusterInfo = runningClusterInfoList.find(clusterInfo => clusterInfo.task_id == runningTaskId.id)
                    if (!clusterInfo) {
                        runningTaskIdListWithReset.push(runningTaskId)
                    }
                })
                let resetClusterRes = await clusterApi.resetCluster(runningClusterInfoList);
            }

            // 对正在进程的任务进行重置操作 只用map会导致[nul]的情况
            let normalTaskIdList = this.multipleSelection
                .filter(item => item.state != '正在进行')
                .map(item => ({
                    id: item.id,
                    state: "等待",
                    error_info: "",
                    final_result: '',
                    pc_id: '',
                }));
            runningTaskIdListWithReset = runningTaskIdListWithReset.map(item => {
                return {
                    id: item.id,
                    state: "等待",
                    error_info: "",
                    final_result: '',
                    pc_id: '',
                }
            })
            normalTaskList = normalTaskList.concat(runningTaskIdListWithReset)
            if (normalTaskIdList.length > 0) {
                await courseApi.updateCourseTask(normalTaskIdList);
                await courseApi.clearCourseLog(normalTaskIdList);
            }

            // 更新列表
            await this.updateTaskList()

            // 关闭loading
            this.loading = false
            this.$message.success(`成功重置${length}条数据 `);
        },
        // 导出数据
        async exportData() {
            //从服务器抓取数据
            let taskListResult = await courseApi.getCourseList({
                currentPage: 1,
                pageSize: 2000,
                searchData: {
                    username: this.usernameSearch.trim(),
                    comment: this.commentSearch.trim(),
                    state: this.stateSearch.trim(),
                },
                platform: this.meta.platform,
                host: location.host,
            });

            //更新本地数据 taskList
            let taskList = taskListResult.taskList;
            // 2. 遍历后端返回的列表，根据每个 item 的 final_result 拆分多行
            let excelpage = [];
            taskList.forEach(item => {
                // 处理日志
                if (item.course_info_logs.length == 0) {
                    item.logMessage = ''
                } else {
                    item.logMessage = item.course_info_logs[0]
                }

                // item.course_info_logs.forEach(log => {
                //     item.logMessage += log
                // })
                // 如果 final_result 是一个数组，就逐项拆分
                if (Array.isArray(item.final_result)) {
                    item.final_result.forEach(course => {
                        excelpage.push({
                            username: item.username,
                            password: item.password,
                            schoolname: item.schoolname,
                            platform: item.platform,
                            state: item.state,
                            subject: course.courseName,
                            score: course.progress,
                            comment: item.comment,
                            logMessage: item.logMessage + '',
                        });
                    });
                } else {
                    // 如果没有 final_result 数组，就保持单行（或根据需求自行处理）
                    excelpage.push({
                        username: item.username,
                        password: item.password,
                        schoolname: item.schoolname,
                        platform: item.platform,
                        state: item.state,
                        subject: '',
                        score: '',
                        comment: item.comment,
                        logMessage: item.logMessage
                    });
                }
            });
            return excelpage
        },

        // ========= 表格内容栏 =========
        //获取表格数据
        async updateTaskList() {
            //从服务器抓取数据
            let taskListResult = await courseApi.getCourseList({
                currentPage: this.currentPage,
                pageSize: this.pageSize,
                searchData: {
                    username: this.usernameSearch.trim(),
                    comment: this.commentSearch.trim(),
                    state: this.stateSearch.trim(),
                },
                platform: this.meta.platform,
                host: location.host,
            });


            //更新本地数据 taskList
            let taskList = taskListResult.taskList;
            taskList.forEach(item => {
                // 日志
                item.logMessage = ''
                item.course_info_logs.forEach(log => {
                    item.logMessage += log
                })

                // 进度信息
                item.progressMsg = ''
                if (Array.isArray(item.final_result)) {
                    item.final_result.forEach(course => {
                        let color = course.progress >= this.meta.passProgress ? 'black' : 'red'
                        item.progressMsg += `<span style="color:${color}">【${course.courseName}：${course.progress}%】·</span>`
                    })
                }
            });


            //更新本地数据 taskList
            this.taskList = taskList;
            this.refreshContent = this.autoRefresh == true ? '关闭刷新' : '开启刷新'
            //更新本地页码数据
            this.total = taskListResult.pagination.total;
        },

        //上传头像
        handleAvatarSuccess(res, file) {
            // console.log('成功上传')
            //不涉及上传，所这里为空
        },
        async beforeAvatarUpload(file, data) {
            //用blueimp-load-image模块处理图片，先转为canvas
            const vm = this;

            await new Promise((resolve, reject) => {
                loadImage(
                    file,
                    (canvas) => { // 使用箭头函数，使得 this 指向 Vue 实例
                        if (canvas.type === "error") {
                            console.log("Error loading image");
                        } else {
                            //判断格式
                            if (file.type != 'image/jpeg') {
                                vm.$message.error('上传头像图片只能是 JPG 格式!');
                                return;
                            }

                            //判断文件大小
                            if (file.size / 1024 > 50) {
                                vm.$message.error('上传头像图片大小不能超过 50kb!');
                                return;
                            }

                            //判断像素尺寸
                            const radio = canvas.width / canvas.height;
                            const targetRatio = 3 / 4;
                            const tolerance = 0.01;
                            if (Math.abs(radio - targetRatio) > tolerance) {
                                vm.$message.error('上传尺寸的长宽像素比只能是3/4');
                                return;
                            }

                            // 转为base64
                            let base64Data = canvas.toDataURL("image/jpeg");
                            data.photoBase64 = base64Data

                            //通过bolb对象转为url,用于前端显示
                            canvas.toBlob((blob) => {
                                // 创建对象 URL
                                data.imageUrl = URL.createObjectURL(blob);
                            }, "image/jpeg");

                            //最后返回promise
                            resolve()
                        }
                    },
                    {
                        maxWidth: 300,
                        maxHeight: 400,
                        canvas: true
                    }
                );
            })

            //把base64上传到数据库
            await examApi.uploadImage({
                platform: data.platform,
                school: data.schoolname,
                username: data.username,
                photoBase64: data.photoBase64
            })
            //始终返回false，不进行上传操作
            return false
        },
        async upMeta() {
            let path = location.pathname.slice(8)
            switch (path) {
                case 'nmwcxt':
                    this.meta.platform = 'nmwcxt';
                    this.meta.name = '柠檬文才学堂网课';
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 40
                    break;
                case 'ahjxjy':
                    this.meta.platform = 'ahjxjy';
                    this.meta.name = '继续教育网课';
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 95
                    break;
                case 'ahjxjyv2':
                    this.meta.platform = 'ahjxjyv2';
                    this.meta.name = '继续教育网课（新）';
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 95
                    break;
                case 'jrxx':
                    this.meta.platform = 'jrxx';
                    this.meta.name = '君睿信息网课';
                    this.meta.hasPhotoColumn = true;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 95
                    break;
                case 'cxxxt':
                    this.meta.platform = 'cxxxt';
                    this.meta.name = '超星学习通网课'
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = true;
                    this.meta.passProgress = 95
                    break;
                case 'hcjy':
                    this.meta.platform = 'hcjy';
                    this.meta.name = '弘成教育网课'
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 95
                    break;
                case 'gjkfdx':
                    this.meta.platform = 'gjkfdx';
                    this.meta.name = '国开网课'
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasVideoRateColumn = false
                    this.meta.passProgress = 95
                    break;
                default:
                    break;
            }
            await this.updateTaskList();
        },


        // ========= 任务日志弹窗 =========
        //显示日志
        async openLogger(item) {
            let courseLog = await courseApi.getCourseLog({ id: item.id })
            let logMessage = ''
            courseLog.forEach(log => {
                logMessage += log
            })
            this.logInfo = logMessage
            this.showLoginfoDialogVisible = true;
        },

        // ========= 错误信息弹窗 =========
        //显示错误日志
        openErrorLogger(data) {
            this.errorInfo = data;
            this.showErrorDialogVisible = true;
        },

        // ========= 添加任务弹窗 =========
        //格式化数据 '2022022，121312,合肥工业大学。'=>{username:'2022022',password:'121312',schoolname:'合肥工业大学'}
        normalizedData() {
            // 定义字段名
            const fieldNames = ['username', 'password', 'schoolname', 'year', 'term', 'coursename', 'others'];

            // 处理数据字符串，转换为对象数组
            function parseData(dataStr) {
                // 去除首尾的空白字符
                const trimmedData = dataStr.trim();

                // 按换行符拆分成数组
                const lines = trimmedData.split('\n');

                // 处理每一行数据
                const result = lines.map(line => {
                    // 按制表符拆分字段
                    const fields = line.split('\t');

                    // 将字段映射到对象
                    const obj = {};
                    fieldNames.forEach((fieldName, index) => {
                        let value = fields[index] ? fields[index].trim() : ''
                        if (value === '\\') value = ''
                        obj[fieldName] = value
                    });

                    return obj;
                });

                return result;
            }

            // 生成 HTML 表格字符串
            function generateHTMLTable(dataArray, headers) {
                // 开始构建表格
                let html = '<table border="1" cellspacing="0" cellpadding="5">\n';

                // 构建表头
                html += '  <thead>\n    <tr>\n';
                headers.forEach(header => {
                    html += `      <th>${escapeHTML(header)}</th>\n`;
                });
                html += '    </tr>\n  </thead>\n';

                // 构建表体
                html += '  <tbody>\n';
                dataArray.forEach(row => {
                    html += '    <tr>\n';
                    headers.forEach(field => {
                        html += `      <td>${escapeHTML(row[field])}</td>\n`;
                    });
                    html += '    </tr>\n';
                });
                html += '  </tbody>\n';

                // 结束表格
                html += '</table>';

                return html;
            }

            // 辅助函数：转义 HTML 特殊字符，防止 XSS
            function escapeHTML(str) {
                if (typeof str !== 'string') {
                    return str;
                }
                return str.replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }

            // 使用函数处理数据
            let dataArray = parseData(this.courseData);

            // 生成 HTML 表格字符串
            let htmlTableString = generateHTMLTable(dataArray, fieldNames);
            this.courseData = JSON.stringify(dataArray);
            this.courseHtml = htmlTableString;
            this.buttonDisabled = false;
        },

        // ========= 页码栏 =========
        //改变每页显示数量
        async sizeChange(size) {
            this.loading = true
            this.loadingText = '正在加载数据...';
            this.loadingDuration = 2000; // 预计2秒
            this.pageSize = size;
            await this.updateTaskList();
            this.loading = false
            this.$message.warning(`当前为第 ${this.currentPage}页，每页显示 ${this.pageSize} 条数据`);
        },
        //改变页码
        async currentChange(current) {
            this.loading = true
            this.loadingText = '正在加载数据...';
            this.loadingDuration = 2000; // 预计2秒
            this.currentPage = current;
            await this.updateTaskList();
            this.loading = false
            this.$message.warning(`当前为第 ${this.currentPage}页，每页显示 ${this.pageSize} 条数据`);
        },


    },

    async created() {
        await this.upMeta();
        setInterval(() => {
            if (this.autoRefresh) {
                this.autoRefresh = false
            }
        }, 10 * 60 * 1000)
    },
    async mounted() {
        let that = this
        setInterval(() => {
            if (that.autoRefresh) {
                that.updateTaskList()
            }
        }, 2000)
        if (location.href.includes('coursebak')) {
            this.isAdmin = true;
        }
    },
    components: {
        'downloadExcel': JsonExcel,
        'Loading': Loading
    },
    watch: {
        $route: 'upMeta'
    },

};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

.control {
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 10px;

    .left {
        float: left;
        .el-input {
            width: 150px;
        }
        .el-select {
            width: 80px;
        }
    }

    .right {
        float: right;
        display: flex;
        width: 700px;
        justify-content: space-between;
        & > * {
            flex: 1 1 auto;
            margin-right: 5px;
        }
        .el-input {
            width: 300px;
        }
    }
}

.content_button {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    & > * {
        margin-top: 5px;
    }
}

.logger {
    text-align: left;
    line-height: 15px;
    height: 105px;
    overflow: auto;
}
</style>