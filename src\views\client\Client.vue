<template>
    <div class="article-list">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;客户端</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="getClientList">刷新页面</el-button>
                <el-button type="primary" icon="el-icon-delete" size="medium" @click="delClient">删除选中</el-button>
            </div>

        </div>

        <!-- 内容栏 -->
        <el-table :data="clientList" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="pcId" prop="pcId" width="200" align="center" />
            <el-table-column label="线程数量" prop="clusterSize"  align="center" />
            <el-table-column label="列队数量" prop="queueLength"  align="center" />
        </el-table>

    </div>
</template>

<script>
import * as clientApi from '../../api/clientApi.js';
export default {
    data() {
        return {
            // 表格相关
            taskQueue: [], //表格数据
            multipleSelection: [], //表格多选

            //控制栏
            // 任务栏下拉列表
            clientList: [],
        };
    },
    methods: {
        // ========= 控制栏 =========
        // 删除选中
        async delClient() {
            for (let clientObj of this.multipleSelection) {
                let data={
                    pcId:clientObj.pcId
                }
                await clientApi.delClient(data)
            }
            await this.getClientList()
        },

        // ========= 表格内容栏 =========
        // 获取客户端列表
        async getClientList() {
            let clientList = await clientApi.getClientList();
            for (let clientObj of clientList) {
                clientObj.queueLength = clientObj.taskQueue.length
            }
            this.clientList = clientList
        },


    },
    async created() {
        await this.getClientList();
    },
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

.control {
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 10px;

    .left {
        float: left;
        .el-input {
            width: 150px;
        }
    }

    .right {
        float: right;
        display: flex;
        width: 500px;
        justify-content: space-between;
        & > * {
            flex: 1 1 auto;
            margin-right: 5px;
        }
        .el-input {
            width: 300px;
        }
    }
}

.content_button {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    & > * {
        margin-top: 5px;
    }
}

.logger {
    text-align: left;
    line-height: 15px;
    height: 100px;
    overflow: auto;
}
</style>