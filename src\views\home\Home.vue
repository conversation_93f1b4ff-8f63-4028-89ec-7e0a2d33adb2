<template>
    <div class="home-dashboard">
        <!-- 进程统计区域 -->
        <div class="process-stats">
            <h2 class="section-title">进程</h2>
            <div class="stats-cards">
                <div class="stat-card">
                    <i class="el-icon-monitor"></i>
                    <div class="stat-info">
                        <div class="stat-label">客户端</div>
                        <div class="stat-value">{{processStats.clientCount}}台</div>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="el-icon-cpu"></i>
                    <div class="stat-info">
                        <div class="stat-label">总进程</div>
                        <div class="stat-value">{{processStats.totalProcess}}个</div>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="el-icon-loading"></i>
                    <div class="stat-info">
                        <div class="stat-label">运行</div>
                        <div class="stat-value">{{processStats.runningProcess}}个</div>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="el-icon-time"></i>
                    <div class="stat-info">
                        <div class="stat-label">空闲</div>
                        <div class="stat-value">{{processStats.idleProcess}}个</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网课统计区域 -->
        <div class="course-section">
            <h2 class="section-title">网课</h2>
            <div class="section-content">
                <!-- 左侧统计卡片 -->
                <div class="task-summary">
                    <!-- 主要统计数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">任务总数:</span>
                            <span class="summary-value total">{{courseStats.total}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">已完成:</span>
                            <span class="summary-value completed">{{courseStats.completed}}</span>
                        </div>
                    </div>

                    <!-- 进行中的任务数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">正在进行:</span>
                            <span class="summary-value in-progress">{{courseStats.inProgress}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">等待:</span>
                            <span class="summary-value waiting">{{courseStats.waiting}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">列队中:</span>
                            <span class="summary-value queued">{{courseStats.queued}}</span>
                        </div>
                    </div>

                    <!-- 失败和断开连接数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">失败:</span>
                            <span class="summary-value failed">{{courseStats.failed}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">服务器中断:</span>
                            <span class="summary-value disconnected">{{courseStats.disconnected}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">客户端中断:</span>
                            <span class="summary-value client-disconnected">{{courseStats.clientDisconnected}}</span>
                        </div>
                    </div>
                </div>

                <!-- 右侧图表和表格 -->
                <div class="chart-table-container">
                    <!-- 柱状图 -->
                    <div ref="courseChart" class="chart-container"></div>

                    <!-- 表格 -->
                    <el-table :data="courseTableData" class="data-table" size="mini" :fit="true">
                        <el-table-column prop="school" label="" min-width="120"></el-table-column>
                        <el-table-column v-for="school in courseTableColumns" :key="school" :label="school" align="center" min-width="80">
                            <template slot-scope="{ row }">{{ row[school] }}</template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <!-- 考试统计区域 -->
        <div class="exam-section">
            <h2 class="section-title">考试</h2>
            <div class="section-content">
                <!-- 左侧统计卡片 -->
                <div class="task-summary">
                    <!-- 主要统计数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">任务总数:</span>
                            <span class="summary-value total">{{examStats.total}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">已完成:</span>
                            <span class="summary-value completed">{{examStats.completed}}</span>
                        </div>
                    </div>

                    <!-- 进行中的任务数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">正在进行:</span>
                            <span class="summary-value in-progress">{{examStats.inProgress}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">等待:</span>
                            <span class="summary-value waiting">{{examStats.waiting}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">列队中:</span>
                            <span class="summary-value queued">{{examStats.queued}}</span>
                        </div>
                    </div>

                    <!-- 失败和断开连接数据 -->
                    <div class="summary-group">
                        <div class="summary-item">
                            <span class="summary-label">失败:</span>
                            <span class="summary-value failed">{{examStats.failed}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">服务器中断:</span>
                            <span class="summary-value disconnected">{{examStats.disconnected}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">客户端中断:</span>
                            <span class="summary-value client-disconnected">{{examStats.clientDisconnected}}</span>
                        </div>
                    </div>
                </div>

                <!-- 右侧图表和表格 -->
                <div class="chart-table-container">
                    <!-- 柱状图 -->
                    <div ref="examChart" class="chart-container"></div>

                    <!-- 表格 -->
                    <el-table :data="examTableData" class="data-table" size="mini" :fit="true">
                        <el-table-column prop="school" label="" min-width="120"></el-table-column>
                        <el-table-column v-for="school in examTableColumns" :key="school" :label="school" align="center" min-width="80">
                            <template slot-scope="{ row }">{{ row[school] }}</template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as clusterApi from '@/api/clusterApi.js';
import * as clientApi from '@/api/clientApi.js';
import * as courseApi from '@/api/courseApi.js';
import * as examApi from '@/api/examApi.js';
import * as echarts from 'echarts';

export default {
    name: 'Home',
    data() {
        return {
            // 进程统计数据
            processStats: {
                clientCount: 0,      // 客户端数量
                totalProcess: 0,     // 总进程数
                runningProcess: 0,   // 运行中进程
                idleProcess: 0       // 空闲进程
            },

            // 网课统计数据
            courseStats: {
                total: 0,            // 任务总数
                completed: 0,        // 已完成
                inProgress: 0,       // 正在进行
                queued: 0,           // 列队中
                failed: 0,           // 失败
                waiting: 0,          // 等待
                disconnected: 0,     // 服务器中断
                clientDisconnected: 0 // 客户端中断
            },

            // 网课表格动态列数据
            courseTableColumns: [],
            courseTableData: [],

            // 考试统计数据
            examStats: {
                total: 0,            // 任务总数
                completed: 0,        // 已完成
                inProgress: 0,       // 正在进行
                queued: 0,           // 列队中
                failed: 0,           // 失败
                waiting: 0,          // 等待
                disconnected: 0,     // 服务器中断
                clientDisconnected: 0 // 客户端中断
            },

            // 考试表格动态列数据
            examTableColumns: [],
            examTableData: [],

            // 图表实例g
            courseChartInstance: null,
            examChartInstance: null,

            // 定时器
            refreshTimer: null,

            // 平台映射
            platformMap: {
                'ahjxjy': '继续教育在线',
                'ahjxjyv2': '新版继续教育在线',
                'cxxxt': '超星学习通',
                'gjkfdx': '国家开放大学',
                'hcjy': '弘成教育',
                'nmwc': '柠檬文才',
                'nmwcxt': '柠檬文才学堂',
                'jrxx': '君睿信息',
                'gkrj':'国开软件学院'
            }
        };
    },

    methods: {
        // 获取进程统计数据
        async fetchProcessStats() {
            try {
                // 获取客户端列表
                const clientResult = await clientApi.getClientList();
                this.processStats.clientCount = clientResult.length;

                // 获取进程统计数据
                const clusterResult = await clusterApi.getClusterStat();
                const { totalCount, idleCount } = clusterResult;
                this.processStats.totalProcess = totalCount;
                this.processStats.idleProcess = idleCount;
                this.processStats.runningProcess = totalCount - idleCount; // 运行中 = 总数 - 空闲
            } catch (error) {
                this.$message.error('获取进程数据失败，请刷新重试');
            }
        },

        // 获取网课统计数据
        async fetchCourseStats() {
            try {
                const result = await courseApi.getCourseStat();

                const { totalCount, stateStat, platformStat, platformSchoolStat } = result;

                // 更新统计数据
                this.courseStats.total = totalCount;
                this.courseStats.completed = stateStat['完成'] || 0;
                this.courseStats.inProgress = stateStat['正在进行'] || 0; 
                this.courseStats.queued = stateStat['列队中'] || 0;     
                this.courseStats.failed = stateStat['失败'] || 0; 
                this.courseStats.waiting = stateStat['等待'] || 0; 
                this.courseStats.disconnected = stateStat['服务器中断'] || 0; 
                this.courseStats.clientDisconnected = stateStat['客户端中断'] || 0; 

                // 处理图表数据 - 直接传递platformStat
                const chartData = {};

                // 遍历平台统计数据，处理状态映射
                Object.keys(platformStat).forEach(platform => {
                    chartData[platform] = {
                        completed: platformStat[platform]['完成'] || 0,
                        inProgress: platformStat[platform]['等待'] || 0,  // "等待"映射为"正在进行"
                        queued: platformStat[platform]['列队中'] || 0,
                        failed: platformStat[platform]['失败'] || 0
                    };
                });

                console.log('处理后的图表数据:', chartData); // 调试日志

                // 初始化图表
                this.initCourseChart(chartData);

                // 更新图表
                this.updateCourseChart(chartData);

                // 处理表格数据
                this.updateCourseTable(platformSchoolStat);

            } catch (error) {
                this.$message.error('获取网课数据失败，请刷新重试');
            }
        },

        // 初始化网课图表
        initCourseChart(chartData) {
            this.courseChartInstance = echarts.init(this.$refs.courseChart);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['已完成', '正在进行', '列队中','失败'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    // data: ['继续教育在线', '超星学习通', '国家开放大学']
                    data: Object.keys(chartData).map(key => this.platformMap[key])
                },
                yAxis: {
                    type: 'value',
                    max: 120
                },
                series: [
                    {
                        name: '已完成',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#67C23A' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '正在进行',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#E6A23C' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '列队中',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#409EFF' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '失败',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#F56C6C' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    }
                ]
            };
            this.courseChartInstance.setOption(option);
        },

        // 使用统计数据更新网课图表
        updateCourseChart(chartData) {
            // 从API统计数据构建图表数据
            const completedData = Object.values(chartData).map(item => item.completed);
            const inProgressData = Object.values(chartData).map(item => item.inProgress);
            const queuedData = Object.values(chartData).map(item => item.queued);
            const failedData = Object.values(chartData).map(item => item.failed);

            // 计算最大值用于Y轴范围
            const maxValue = Math.max(...completedData, ...inProgressData, ...queuedData, 20);

            // 更新图表
            if (this.courseChartInstance) {
                this.courseChartInstance.setOption({
                    yAxis: {
                        type: 'value',
                        max: Math.ceil(maxValue * 1.2) // 留出20%的空间
                    },
                    series: [
                        {
                            name: '已完成',
                            data: completedData
                        },
                        {
                            name: '正在进行',
                            data: inProgressData
                        },
                        {
                            name: '列队中',
                            data: queuedData
                        },
                        {
                            name: '失败',
                            data: failedData
                        }
                    ]
                });
            }
        },

        // 使用统计数据更新网课表格
        updateCourseTable(platformSchoolStat) {
            // 根据 platformSchoolStat 动态生成表格列和数据
            const schools = Array.from(new Set(Object.values(platformSchoolStat).flatMap(stat => Object.keys(stat))));
            this.courseTableColumns = schools;
            this.courseTableData = Object.entries(platformSchoolStat).map(([platformCode, stat]) => {
                const row = { school: this.platformMap[platformCode] || platformCode };
                schools.forEach(sch => { row[sch] = stat[sch] || 0; });
                return row;
            });
        },

        // 获取考试统计数据
        async fetchExamStats() {
            try {
                const result = await examApi.getExamStat();

                const { totalCount, stateStat, platformStat, platformSchoolStat } = result;

                // 更新统计数据
                this.examStats.total = totalCount;
                this.examStats.completed = stateStat['完成'] || 0;
                this.examStats.inProgress = stateStat['等待'] || 0; // API中的"等待"对应前端的"正在进行"
                this.examStats.queued = stateStat['列队中'] || 0;

                // 处理图表数据 - 直接传递platformStat
                const chartData = {};

                // 遍历平台统计数据，处理状态映射
                Object.keys(platformStat).forEach(platform => {
                    chartData[platform] = {
                        completed: platformStat[platform]['完成'] || 0,
                        inProgress: platformStat[platform]['等待'] || 0,  // "等待"映射为"正在进行"
                        queued: platformStat[platform]['列队中'] || 0
                    };
                });

                // console.log('处理后的考试图表数据:', chartData); // 调试日志
                this.initExamChart(chartData);

                // 更新图表
                this.updateExamChart(chartData);

                // 处理表格数据
                this.updateExamTable(platformSchoolStat);

            } catch (error) {
                this.$message.error('获取考试数据失败，请刷新重试');
            }
        },

        // 初始化考试图表
        initExamChart(chartData) {
            this.examChartInstance = echarts.init(this.$refs.examChart);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['已完成', '正在进行', '列队中','失败'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: Object.keys(chartData).map(key => this.platformMap[key])
                },
                yAxis: {
                    type: 'value',
                    max: 120
                },
                series: [
                    {
                        name: '已完成',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#67C23A' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '正在进行',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#E6A23C' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '列队中',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#409EFF' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    {
                        name: '失败',
                        type: 'bar',
                        data: [0, 0, 0],
                        itemStyle: { color: '#F56C6C' },
                        label: {
                            show: true,
                            position: 'top',
                            fontSize: 12,
                            color: '#666'
                        }
                    }
                ]
            };
            this.examChartInstance.setOption(option);
        },

        // 使用统计数据更新考试图表
        updateExamChart(chartData) {
            // 从API统计数据构建图表数据
            const ahjxjyData = chartData.ahjxjy || chartData.ahjxjyv2 || { completed: 0, inProgress: 0, queued: 0 };
            const cxxxtData = chartData.cxxxt || { completed: 0, inProgress: 0, queued: 0 };
            const gjkfdxData = chartData.gjkfdx || { completed: 0, inProgress: 0, queued: 0 };

            // 如果没有gjkfdx，可能使用的是其他键名，需要合并相关平台数据
            // 继续教育在线平台可能包含 ahjxjy 和 ahjxjyv2
            const jxjyCompleted = (chartData.ahjxjy?.completed || 0) + (chartData.ahjxjyv2?.completed || 0);
            const jxjyInProgress = (chartData.ahjxjy?.inProgress || 0) + (chartData.ahjxjyv2?.inProgress || 0);
            const jxjyQueued = (chartData.ahjxjy?.queued || 0) + (chartData.ahjxjyv2?.queued || 0);

            // 计算最大值用于Y轴范围
            const maxValue = Math.max(
                jxjyCompleted + jxjyInProgress + jxjyQueued,
                cxxxtData.completed + cxxxtData.inProgress + cxxxtData.queued,
                gjkfdxData.completed + gjkfdxData.inProgress + gjkfdxData.queued,
                20 // 最小值为20
            );

            // 更新图表
            if (this.examChartInstance) {
                this.examChartInstance.setOption({
                    yAxis: {
                        type: 'value',
                        max: Math.ceil(maxValue * 1.2) // 留出20%的空间
                    },
                    series: [
                        {
                            name: '已完成',
                            data: [jxjyCompleted, cxxxtData.completed, gjkfdxData.completed]
                        },
                        {
                            name: '正在进行',
                            data: [jxjyInProgress, cxxxtData.inProgress, gjkfdxData.inProgress]
                        },
                        {
                            name: '列队中',
                            data: [jxjyQueued, cxxxtData.queued, gjkfdxData.queued]
                        }
                    ]
                });
            }
        },

        // 使用统计数据更新考试表格
        updateExamTable(platformSchoolStat) {
            // 根据 platformSchoolStat 动态生成表格列和数据
            const schools = Array.from(new Set(Object.values(platformSchoolStat).flatMap(stat => Object.keys(stat))));
            this.examTableColumns = schools;
            this.examTableData = Object.entries(platformSchoolStat).map(([platformCode, stat]) => {
                const row = { school: this.platformMap[platformCode] || platformCode };
                schools.forEach(sch => { row[sch] = stat[sch] || 0; });
                return row;
            });
        },

        // 刷新所有数据
        async refreshAllData() {
            await Promise.all([
                this.fetchProcessStats(),
                this.fetchCourseStats(),
                this.fetchExamStats()
            ]);
        }
    },

    async created() {
        // // 初始化图表
        // this.initCourseChart();
        // this.initExamChart();

        // 获取初始数据
        await this.refreshAllData();

        // 设置定时刷新（每30秒刷新一次）
        this.refreshTimer = setInterval(() => {
            this.refreshAllData();
        }, 30000);

        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', () => {
            if (this.courseChartInstance) {
                this.courseChartInstance.resize();
            }
            if (this.examChartInstance) {
                this.examChartInstance.resize();
            }
        });
    },

    beforeDestroy() {
        // 清理定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        // 销毁图表实例
        if (this.courseChartInstance) {
            this.courseChartInstance.dispose();
        }
        if (this.examChartInstance) {
            this.examChartInstance.dispose();
        }

        // 移除事件监听
        window.removeEventListener('resize', () => { });
    }
};
</script>

<style lang="scss" scoped>
.home-dashboard {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;

    // 区块标题样式
    .section-title {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;
        padding-left: 10px;
        border-left: 4px solid #409eff;
    }

    // 进程统计区域
    .process-stats {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .stats-cards {
            display: flex;
            justify-content: space-between;
            gap: 20px;

            .stat-card {
                flex: 1;
                display: flex;
                align-items: center;
                padding: 20px;
                background-color: #f0f9ff;
                border-radius: 8px;
                border: 1px solid #d1e9ff;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
                }

                i {
                    font-size: 48px;
                    color: #409eff;
                    margin-right: 15px;
                }

                .stat-info {
                    .stat-label {
                        font-size: 14px;
                        color: #909399;
                        margin-bottom: 5px;
                    }

                    .stat-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #303133;
                    }
                }
            }
        }
    }

    // 网课和考试区域共同样式
    .course-section,
    .exam-section {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .section-content {
            display: flex;
            gap: 20px;
            min-height: 550px; // 确保内容区域有足够的高度

            // 左侧统计卡片 - 改进后的样式
            .task-summary {
                width: 280px;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                gap: 15px;
                height: 100%; // 占满整个高度
                justify-content: space-between; // 均匀分布各组统计数据

                // 每组统计数据的容器
                .summary-group {
                    flex: 1; // 平均分配空间
                    padding: 15px;
                    background-color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    border-left: 4px solid #dcdfe6;
                    display: flex;
                    flex-direction: column;
                    justify-content: center; // 垂直居中内容

                    // 根据分组类型设置不同的左侧边框颜色
                    &:nth-child(1) {
                        border-left-color: #409eff; // 主要统计数据 - 蓝色
                    }

                    &:nth-child(2) {
                        border-left-color: #e6a23c; // 进行中任务 - 橙色
                    }

                    &:nth-child(3) {
                        border-left-color: #f56c6c; // 失败任务 - 红色
                    }
                }

                // 单个统计项
                .summary-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    position: relative;

                    // 除最后一项外，添加底部分隔线
                    &:not(:last-child) {
                        border-bottom: 1px dashed #ebeef5;
                        margin-bottom: 8px; // 增加项目间的间距
                    }

                    // 标签样式
                    .summary-label {
                        font-size: 14px;
                        color: #606266;
                        flex-shrink: 0;
                    }

                    // 数值样式 - 增强版
                    .summary-value {
                        font-size: 18px;
                        font-weight: bold;
                        margin-left: 8px;
                        padding: 2px 8px;
                        border-radius: 4px;
                        background-color: rgba(0, 0, 0, 0.05);

                        // 不同状态的颜色
                        &.total {
                            color: #409eff;
                            background-color: rgba(64, 158, 255, 0.1);
                        }

                        &.completed {
                            color: #67c23a;
                            background-color: rgba(103, 194, 58, 0.1);
                        }

                        &.in-progress,
                        &.waiting {
                            color: #e6a23c;
                            background-color: rgba(230, 162, 60, 0.1);
                        }

                        &.queued {
                            color: #909399;
                            background-color: rgba(144, 147, 153, 0.1);
                        }

                        &.failed {
                            color: #f56c6c;
                            background-color: rgba(245, 108, 108, 0.1);
                        }

                        &.disconnected,
                        &.client-disconnected {
                            color: #f56c6c;
                            background-color: rgba(245, 108, 108, 0.1);
                        }
                    }
                }
            }

            // 右侧图表和表格容器
            .chart-table-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 20px;
                min-width: 0;
                height: 100%; // 占满整个高度

                // 图表容器
                .chart-container {
                    height: 300px;
                    border: 1px solid #e4e7ed;
                    border-radius: 8px;
                    padding: 10px;
                    background: linear-gradient(to bottom, #ffffff, #f9fafc);
                }

                // 表格样式改进
                .data-table {
                    border-radius: 8px;
                    overflow: hidden;
                    width: 100%;
                    flex: 1; // 表格占据剩余空间
                    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
                    border: 1px solid #ebeef5;

                    /deep/ .el-table__header {
                        background-color: #f0f9ff;
                        
                        th {
                            background-color: #f0f9ff;
                            color: #606266;
                            font-weight: bold;
                            padding: 12px 0;
                            border-bottom: 2px solid #d1e9ff;
                        }
                    }

                    /deep/ .el-table__body {
                        td {
                            padding: 12px 0;
                        }
                    }

                    /deep/ .el-table__row {
                        &:hover {
                            background-color: #f5f7fa;
                        }
                        
                        // 隔行变色
                        &:nth-child(even) {
                            background-color: #fafafa;
                        }
                        
                        // 突出显示第一列（平台名称列）
                        td:first-child {
                            font-weight: bold;
                            color: #303133;
                            border-right: 1px solid #ebeef5;
                            background-color: #f5f7fa;
                        }
                        
                        // 数值列居中并加样式
                        td:not(:first-child) {
                            text-align: center;
                            font-weight: 500;
                        }
                    }

                    /deep/ .el-table__cell {
                        padding: 8px;
                    }
                    
                    // 表格外边框圆角
                    /deep/ .el-table__fixed::before,
                    /deep/ .el-table__fixed-right::before {
                        background-color: transparent;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .home-dashboard {
        .process-stats {
            .stats-cards {
                flex-wrap: wrap;

                .stat-card {
                    flex: 0 0 calc(50% - 10px);
                }
            }
        }

        .course-section,
        .exam-section {
            .section-content {
                flex-direction: column;
                height: auto; // 在小屏幕上自适应高度

                .task-summary {
                    width: 100%;
                    display: flex;
                    flex-direction: row; // 在小屏幕上改为行布局
                    flex-wrap: wrap; // 允许换行
                    height: auto; // 自适应高度
                    
                    .summary-group {
                        flex: 1;
                        min-width: 280px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .home-dashboard {
        padding: 10px;

        .process-stats {
            .stats-cards {
                .stat-card {
                    flex: 0 0 100%;
                    margin-bottom: 10px;
                }
            }
        }

        .course-section,
        .exam-section {
            .section-content {
                .task-summary {
                    .summary-group {
                        min-width: 100%; // 在更小的屏幕上占满宽度
                    }
                }
            }
        }
    }
}
</style>