{"name": "backstage", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "webpack --config webpack.config.js", "obfuscate": "javascript-obfuscator ./dist --output ./dist --config ./obfuscator-config.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.22.20", "@babel/preset-env": "^7.22.20", "@vue/babel-preset-jsx": "^1.4.0", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.32.2", "css-loader": "^6.8.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "node-sass": "^9.0.0", "sass-loader": "^13.3.2", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.7.14", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.2", "axios": "^1.7.3", "blueimp-load-image": "^5.16.0", "blueimp-md5": "^2.19.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-ui": "^2.15.14", "javascript-obfuscator": "^4.1.1", "uuid": "^9.0.1", "vue": "^2.7.14", "vue-json-excel": "^0.3.0", "vue-router": "^3.6.5", "vuex": "^3.6.2"}}