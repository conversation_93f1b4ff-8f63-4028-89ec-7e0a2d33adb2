#基于1panel的openresty配置

name: course-web-deploy # 工作流的名称

on:
    push:
        branches:
            - main # 触发的分支，可以根据需要修改为其他分支

jobs:
    deploy:
        name: 基于1panel的openresty配置，直接打包复制最终项目文件即可
        runs-on: ubuntu-latest
        strategy:
            fail-fast: false # 确保所有服务器都尝试部署
            matrix:
                server_ip: ['*************']

        steps:
            - name: 1.从仓库中检出当前 commit 的所有文件
              uses: actions/checkout@v3

            - name: 2. 安装依赖
              run: npm install

            - name: 3. 构建项目（打包）
              run: npm run build

            - name: 4.复制未混淆的网站到服务器 coursebak.mozhi0012.top
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ matrix.server_ip }}
                  username: ${{ secrets.SERVER_USER }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
                  source: 'dist/*'
                  target: '/opt/1panel/apps/openresty/openresty/www/sites/coursebak.mozhi0012.top/index'
                  recursive: true
                  overwrite: true
                  # 最终目录就是 target+(source-strip_components)，这里就是/etc/nginx/custom/
                  strip_components: 1

            - name: 5. 代码混淆
              run: npm run obfuscate

            - name: 6. 复制混淆网站到服务器 course.mozhi0012.top
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ matrix.server_ip }}
                  username: ${{ secrets.SERVER_USER }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
                  source: 'dist/*'
                  target: '/opt/1panel/apps/openresty/openresty/www/sites/course.mozhi0012.top/index'
                  recursive: true
                  overwrite: true
                  # 最终目录就是 target+(source-strip_components)，这里就是/etc/nginx/custom/
                  strip_components: 1

            - name: 7. 复制混淆网站到服务器 xxhelper.top
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ matrix.server_ip }}
                  username: ${{ secrets.SERVER_USER }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
                  source: 'dist/*'
                  target: '/opt/1panel/apps/openresty/openresty/www/sites/xxhelper.top/index'
                  recursive: true
                  overwrite: true
                  # 最终目录就是 target+(source-strip_components)，这里就是/etc/nginx/custom/
                  strip_components: 1



# # 基于docker部署
# name: course-web-deploy # 工作流的名称

# on:
#     push:
#         branches:
#             - main # 触发的分支，可以根据需要修改为其他分支

# jobs:
#     build:
#         name: 构建并推送 Docker镜像到GHCR
#         runs-on: ubuntu-latest
#         steps:
#             - name: 从仓库中检出当前 commit 的所有文件
#               uses: actions/checkout@v3

#             - name: 设置 Docker Buildx 从而支持多平台构建或更高级的构建功能
#               uses: docker/setup-buildx-action@v2

#             - name: 登录到 GHCR
#               uses: docker/login-action@v2
#               with:
#                   registry: ghcr.io
#                   username: ${{ secrets.GHCR_USER }}
#                   password: ${{ secrets.GHCR_TOKEN }}

#             - name: 使用 Dockerfile 构建镜像，这样只能构建单平台
#               run: |
#                   docker build -t ghcr.io/mozhi012/course-web:latest .

#             - name: 将构建好的镜像推送到 GHCR
#               run: |
#                   docker push ghcr.io/mozhi012/course-web:latest

#     deploy:
#         name: 把dokcer镜像部署到多个服务器
#         needs: build
#         runs-on: ubuntu-latest
#         strategy:
#             fail-fast: false # 确保所有服务器都尝试部署
#             matrix:
#                 server_ip: ['*************']

#         steps:
#             - name: 从仓库中检出当前 commit 的所有文件
#               uses: actions/checkout@v3

#             - name: 复制Nginx自定义配置到服务器
#               uses: appleboy/scp-action@v0.1.4
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   port: ${{ secrets.SSH_PORT }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
#                   source: 'config/nginxCustomConfig/*'
#                   target: '/etc/nginx/custom'
#                   recursive: true
#                   overwrite: true
#                   # 最终目录就是 source+(target-strip_components)，这里就是/etc/nginx/custom/
#                   strip_components: 2

#             - name: 复制本地 docker-compose.yml 文件到服务器
#               uses: appleboy/scp-action@v0.1.4
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   port: ${{ secrets.SSH_PORT }}
#                   source: ./docker-compose.yml
#                   target: ~/compose/courseWeb/
#                   overwrite: true

#             - name: 复制本地 compose.env.prod 文件到服务器
#               uses: appleboy/scp-action@v0.1.4
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   port: ${{ secrets.SSH_PORT }}
#                   source: ./compose.env.prod
#                   target: ~/compose/courseWeb/
#                   overwrite: true

#             - name: 从 GHCR 拉取镜像到服务器
#               uses: appleboy/ssh-action@v0.1.8
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   port: ${{ secrets.SSH_PORT }}
#                   script: |
#                       echo ${{ secrets.GHCR_TOKEN }} | docker login ghcr.io -u ${{ secrets.GHCR_USER }} --password-stdin
#                       docker pull ghcr.io/mozhi012/course-web:latest

#             - name: 删除旧的 Docker Compose 容器
#               uses: appleboy/ssh-action@v0.1.8
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   port: ${{ secrets.SSH_PORT }}
#                   script: |
#                       cd ~/compose/courseWeb/
#                       docker-compose --env-file compose.env.prod down

#             - name: 启动新的 Docker Compose 容器
#               uses: appleboy/ssh-action@v0.1.8
#               with:
#                   host: ${{ matrix.server_ip }}
#                   username: ${{ secrets.SERVER_USER }}
#                   key: ${{ secrets.SSH_PRIVATE_KEY }}
#                   port: ${{ secrets.SSH_PORT }}
#                   script: |
#                       cd ~/compose/courseWeb/
#                       docker-compose --env-file compose.env.prod up -d
