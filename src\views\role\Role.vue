<template>
    <div class="article-list" v-loading="loading" element-loading-text="加载中...">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;角色管理</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-plus" size="medium" @click="handleAdd" v-if="isAdmin">新增角色</el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="handleBatchDelete" v-if="isAdmin">批量删除</el-button>
            </div>
            <div class="right">
                <el-input placeholder="请输入角色名称搜索" v-model="searchKeyword" clearable @keyup.enter="handleSearch">
                    <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                </el-input>
            </div>
        </div>

        <!-- 表格内容栏 -->
        <el-table 
            v-if="roleList.length > 0"
            :key="tableKey"
            :data="roleList" 
            :row-key="role => role.role_id" 
            border 
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" prop="role_id" width="80" align="center" />
            <el-table-column label="角色名称" align="center">
                <template slot-scope="scope">
                    <el-link 
                        type="primary" 
                        @click="handlePermission(scope.row)"
                        :underline="false">
                        {{ scope.row.role_name }}
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column label="用户数量" align="center" width="120">
                <template slot-scope="scope">
                    <el-tag type="info">{{ scope.row.user_count }} 个用户</el-tag>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            style="margin-top: 20px; text-align: right;">
        </el-pagination>

        <!-- 新增角色弹窗 -->
        <el-dialog
            title="新增角色"
            :visible.sync="addDialogVisible"
            width="400px"
            @close="resetAddForm">
            <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="100px">
                <el-form-item label="角色名称" prop="role_name">
                    <el-input v-model="addForm.role_name" placeholder="请输入角色名称"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitAddForm">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 权限分配弹窗 -->
        <el-dialog
            :title="`${currentRole.role_name} - 权限分配`"
            :visible.sync="permissionDialogVisible"
            width="800px"
            @close="resetPermissionForm">
            <div v-loading="permissionLoading">
                <el-tree
                    ref="permissionTree"
                    :data="allPermissions"
                    :props="treeProps"
                    node-key="permission_id"
                    default-expand-all
                    show-checkbox
                    :default-checked-keys="checkedPermissionIds">
                    <span class="custom-tree-node" slot-scope="{ data }">
                        <span>
                            <i :class="data.icon" style="margin-right: 8px;"></i>
                            {{ data.name }}
                            <span style="color: #909399; margin-left: 10px;">{{ data.path }}</span>
                        </span>
                    </span>
                </el-tree>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="permissionDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitPermissions">保存权限</el-button>
                <el-button type="danger" @click="handleDeleteInDialog" v-if="isAdmin && currentRole.role_id !== 1">删除角色</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import * as roleApi from '../../api/roleApi.js';

export default {
    data() {
        return {
            loading: false, // 加载状态
            
            // 权限控制
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',
            
            // 表格相关
            roleList: [], // 角色列表数据
            tableKey: 0, // 用于强制重新渲染表格
            multipleSelection: [], // 多选数据
            
            // 搜索相关
            searchKeyword: '', // 搜索关键词
            
            // 分页相关
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            
            // 新增角色弹窗相关
            addDialogVisible: false, // 弹窗显示控制
            addForm: {
                role_name: ''
            },
            addRules: {
                role_name: [
                    { required: true, message: '请输入角色名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
                ]
            },
            
            // 权限分配弹窗相关
            permissionDialogVisible: false, // 权限弹窗显示控制
            permissionLoading: false, // 权限加载状态
            currentRole: {}, // 当前选中的角色
            allPermissions: [], // 所有权限树形结构
            checkedPermissionIds: [], // 已选中的权限ID
            
            // 树形控件配置
            treeProps: {
                children: 'children',
                label: 'name'
            }
        };
    },
    
    methods: {
        // ========= 数据获取 =========
        // 获取角色列表
        async getRoleList() {
            this.loading = true;
            try {
                const params = {
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize,
                    search: this.searchKeyword
                };
                
                const res = await roleApi.getRoleList(params);
                console.log('角色列表响应:', res);
                
                // axios拦截器已经返回了data部分，所以res就是data对象
                if (res) {
                    // 使用深拷贝处理数据，避免引用问题
                    const listData = JSON.parse(JSON.stringify(res.list || []));
                    // 增加表格key，强制重新渲染
                    this.tableKey += 1;
                    // 使用this.$nextTick确保在DOM更新循环结束后再赋值
                    this.$nextTick(() => {
                        this.roleList = listData;
                    });
                    
                    this.pagination.total = res.total || 0;
                    this.pagination.currentPage = res.page || 1;
                    this.pagination.pageSize = res.pageSize || 10;
                }
            } catch (error) {
                console.error('获取角色列表失败:', error);
                this.$message.error('获取角色列表失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },
        
        // 获取角色权限
        async getRolePermissions(roleId) {
            this.permissionLoading = true;
            try {
                const res = await roleApi.getRolePermissions({ role_id: roleId });
                console.log('角色权限响应:', res);
                
                if (res) {
                    // 设置所有权限树
                    this.allPermissions = res.allPermissions || [];
                    // 设置已选中的权限ID
                    this.checkedPermissionIds = res.permissionIds || [];
                }
            } catch (error) {
                console.error('获取角色权限失败:', error);
                this.$message.error('获取角色权限失败');
            } finally {
                this.permissionLoading = false;
            }
        },
        
        // ========= 表格操作 =========
        // 处理多选
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        
        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1;
            this.getRoleList();
        },
        
        // ========= 分页操作 =========
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getRoleList();
        },
        
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.getRoleList();
        },
        
        // ========= CRUD操作 =========
        // 新增角色
        handleAdd() {
            this.addDialogVisible = true;
            this.resetAddForm();
        },
        
        // 提交新增表单
        submitAddForm() {
            this.$refs.addForm.validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    try {
                        await roleApi.createRole(this.addForm);
                        this.$message.success('新增成功');
                        this.addDialogVisible = false;
                        await this.getRoleList();
                    } catch (error) {
                        console.error('新增角色失败:', error);
                        this.$message.error(error || '新增失败，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                } else {
                    console.log('表单验证失败');
                    return false;
                }
            });
        },
        
        // 批量删除
        async handleBatchDelete() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要删除的角色');
                return;
            }
            
            // 检查是否包含管理员角色
            const hasAdminRole = this.multipleSelection.some(role => role.role_id === 1);
            if (hasAdminRole) {
                this.$message.error('不能删除管理员角色');
                return;
            }
            
            // 检查是否有用户的角色
            const hasUserRoles = this.multipleSelection.filter(role => role.user_count > 0);
            if (hasUserRoles.length > 0) {
                const roleNames = hasUserRoles.map(role => role.role_name).join('、');
                this.$message.error(`角色 "${roleNames}" 下还有用户，不能删除`);
                return;
            }
            
            this.$confirm(`确定要删除选中的 ${this.multipleSelection.length} 个角色吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 收集所有要删除的角色ID
                    const roleIds = this.multipleSelection.map(role => role.role_id);
                    
                    // 根据API文档，批量删除时传递所有角色ID
                    await roleApi.deleteRole({ 
                        role_ids: roleIds,
                        role_id: roleIds[0] // 使用第一个ID
                    });
                    
                    this.$message.success(`成功删除角色`);
                    await this.getRoleList();
                } catch (error) {
                    console.error('批量删除失败:', error);
                    this.$message.error(error || '删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },
        
        // 在权限弹窗中删除角色
        async handleDeleteInDialog() {
            // 不能删除管理员角色
            if (this.currentRole.role_id === 1) {
                this.$message.error('不能删除管理员角色');
                return;
            }
            
            // 检查是否有用户
            if (this.currentRole.user_count > 0) {
                this.$message.error(`角色 "${this.currentRole.role_name}" 下还有 ${this.currentRole.user_count} 个用户，不能删除`);
                return;
            }
            
            this.$confirm(`确定要删除角色 "${this.currentRole.role_name}" 吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    await roleApi.deleteRole({ 
                        role_ids: [this.currentRole.role_id],
                        role_id: this.currentRole.role_id 
                    });
                    this.$message.success('删除成功');
                    this.permissionDialogVisible = false; // 关闭弹窗
                    await this.getRoleList();
                } catch (error) {
                    console.error('删除角色失败:', error);
                    this.$message.error(error || '删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },
        
        // 处理权限分配
        async handlePermission(row) {
            this.currentRole = row;
            this.permissionDialogVisible = true;
            await this.getRolePermissions(row.role_id);
        },
        
        // 提交权限分配
        async submitPermissions() {
            this.permissionLoading = true;
            try {
                // 获取选中的权限ID
                const checkedKeys = this.$refs.permissionTree.getCheckedKeys();
                // 获取半选中的权限ID（父节点）
                const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys();
                // 合并所有权限ID
                const allPermissionIds = [...new Set([...checkedKeys, ...halfCheckedKeys])];
                
                const data = {
                    role_id: this.currentRole.role_id,
                    permission_ids: allPermissionIds
                };
                
                await roleApi.assignPermissions(data);
                this.$message.success('权限分配成功');
                this.permissionDialogVisible = false;
                
                // 刷新角色列表（虽然权限变化不影响列表，但保持一致性）
                await this.getRoleList();
            } catch (error) {
                console.error('分配权限失败:', error);
                this.$message.error(error || '分配权限失败，请稍后重试');
            } finally {
                this.permissionLoading = false;
            }
        },
        
        // ========= 表单重置 =========
        // 重置新增表单
        resetAddForm() {
            this.addForm = {
                role_name: ''
            };
            
            this.$nextTick(() => {
                this.$refs.addForm && this.$refs.addForm.clearValidate();
            });
        },
        
        // 重置权限表单
        resetPermissionForm() {
            this.currentRole = {};
            this.allPermissions = [];
            this.checkedPermissionIds = [];
        }
    },
    
    async created() {
        // 页面加载时获取数据
        await this.getRoleList();
    }
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    min-height: calc(100vh - 100px);
}

.user-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409EFF;
}

.control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
        display: flex;
        gap: 10px;
    }
    
    .right {
        .el-input {
            width: 300px;
        }
    }
}

.dialog-footer {
    text-align: right;
}

// 树形视图样式
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

// 加载动画样式
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
}
</style>
